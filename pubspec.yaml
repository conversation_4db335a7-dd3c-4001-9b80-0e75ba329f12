name: controle_fiado_app
description: App para controlar os fiados e fazer cobra<PERSON>

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.2.6+2

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  line_icons: ^2.0.1
  # Removing device_preview due to compatibility issues

  # widgets
  grouped_list: ^5.1.2
  custom_navigation_bar: ^0.8.2
  lite_rolling_switch: ^1.0.1
  flutter_switch: ^0.3.2
  simple_animations: ^5.0.0+2
  supercharged: ^2.1.1

  # manipulação de dados
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  csv: ^5.0.1

  # utilitários
  intl: '>=0.18.0 <0.20.0'
  format: ^1.2.0
  camera: ^0.10.0+3
  collection: ^1.18.0
#  flutter_masked_text: ^0.8.0
  flutter_multi_formatter: ^2.10.2
  toast: ^0.3.0

  # sistema
#  http: ^0.13.5
  share_plus: ^4.5.3
  wakelock: ^0.6.2
  flutter_modular: ^5.0.3
#  url_launcher: ^6.1.6
  permission_handler: ^10.0.2
  whatsapp_share: 2.0.1

  # storage
  external_path: ^1.0.3
  path_provider: ^2.0.11
#  flutter_file_manager: ^0.2.0
  flutter_archive: ^5.0.0
  file_picker: ^5.2.2
  image_picker: ^0.8.6
  image_cropper: ^3.0.0
#  whatsapp_share: ^1.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  build_runner: ^2.2.0
  hive_generator: ^1.1.3

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
