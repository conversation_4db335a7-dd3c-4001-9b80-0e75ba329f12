buildscript {
    ext.kotlin_version = '1.9.0'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

// Fix for plugins missing namespace
subprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            project.android {
                if (namespace == null) {
                    // Set a default namespace based on the project name
                    if (project.name == 'file_picker') {
                        namespace 'com.mr.flutter.plugin.filepicker'
                    } else if (project.name == 'camera_android') {
                        namespace 'io.flutter.plugins.camera'
                    } else if (project.name == 'permission_handler_android') {
                        namespace 'com.baseflow.permissionhandler'
                    } else if (project.name == 'path_provider_android') {
                        namespace 'io.flutter.plugins.pathprovider'
                    } else if (project.name == 'image_picker_android') {
                        namespace 'io.flutter.plugins.imagepicker'
                    } else if (project.name == 'share_plus') {
                        namespace 'dev.fluttercommunity.plus.share'
                    } else {
                        // Fallback namespace
                        namespace project.group ?: "com.example.${project.name}"
                    }
                }
            }
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
