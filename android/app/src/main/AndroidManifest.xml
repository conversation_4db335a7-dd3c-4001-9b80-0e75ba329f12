<manifest
   xmlns:android="http://schemas.android.com/apk/res/android"
   xmlns:tools="http://schemas.android.com/tools"
   package="br.mariodeveloper.controle_fiado.controle_fiado_app"
   >

   <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
   <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
   <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION"/>
   <uses-permission
      android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
      tools:ignore="ScopedStorage"
   />

   <application
      android:label="Seu Fiado"
      android:name="${applicationName}"
      android:icon="@mipmap/ic_launcher"
      android:requestLegacyExternalStorage="true"
      >
      <activity
         android:name=".MainActivity"
         android:exported="true"
         android:launchMode="singleTop"
         android:theme="@style/LaunchTheme"
         android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
         android:hardwareAccelerated="true"
         android:windowSoftInputMode="adjustResize"
         >
         <!-- Specifies an Android theme to apply to this Activity as soon as
              the Android process has started. This theme is visible to the user
              while the Flutter UI initializes. After that, this theme continues
              to determine the Window background behind the Flutter UI. -->
         <meta-data
           android:name="io.flutter.embedding.android.NormalTheme"
           android:resource="@style/NormalTheme"
         />
         <intent-filter>
             <action android:name="android.intent.action.MAIN"/>
             <category android:name="android.intent.category.LAUNCHER"/>
         </intent-filter>
      </activity>

      <activity
         android:name="com.yalantis.ucrop.UCropActivity"
         android:screenOrientation="portrait"
         android:theme="@style/Theme.AppCompat.Light.NoActionBar"
      />
     <!-- Don't delete the meta-data below.
          This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
     <meta-data
         android:name="flutterEmbedding"
         android:value="2"
     />
   </application>
</manifest>
