// ignore_for_file: constant_identifier_names

import 'dart:io' show Directory;
import 'package:intl/intl.dart';
import 'extensions.dart' show FileExtension;

const String app_name = "Controle Fiado";
const String innerBkpFolder = "backup";

String get TODAY => DateFormat("dd/MM/yyy").format(DateTime.now());
String get TODAYmin => DateFormat("yy_MM_dd_${DateFormat.HOUR24_MINUTE}").format(DateTime.now());
String get TODAYm => DateFormat("MM_dd").format(DateTime.now());

abstract class Constants {
  static late String backupFolder;
  static late String imagesFolder;
  static late String tempFolder;
  static late String generalFolder;
  static const String defaultBkpFolder = "storage/emulated/0/Download";
}

abstract class SystemValues {
  static String shopName = "Meu Comércio";
}

abstract class Config {
  static bool defaultBkpActive = true;
  static bool compactDialog = true;
  static bool compactLayout = false;
  static bool decimalValue = false;
  static bool isCancelOut = true;
  /// contém todas as pastas de backups gerais
  static List<String> get bkpGeneralFolders {
    final folder = Constants.generalFolder;
    if (Directory(folder).existsSync()) {
      return Directory(folder).listSync().map<String>((e) => e.name).toList();
    }
    Directory(folder).createSync();
    return [];
  }
}

enum Preference { backup, CMPT_LYT, CMPT_DLG, BKP_FOLDERS, decimal, cancelIn }