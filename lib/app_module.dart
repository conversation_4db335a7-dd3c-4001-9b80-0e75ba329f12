import 'package:controle_fiado_app/features/module_start/submodule_config/config_module.dart';
import 'package:controle_fiado_app/features/module_start/start_controller.dart';
import 'package:controle_fiado_app/features/module_people/person_detail_page.dart';
import 'package:controle_fiado_app/features/module_start/submodule_config/pages/config_home_page.dart';

import 'package:flutter_modular/flutter_modular.dart' show Bind, ChildRout<PERSON>, ModularRoute, Module, ModuleRoute;

import 'features/module_start/start_layout.dart';

class AppModule extends Module {
  @override
  List<Bind> get binds => [
    Bind.singleton((i) => StartController()),
  ];

  @override
  final List<ModularRoute> routes = [
    ChildRoute('/', child: (_, __) => const StartLayout(), children: [
      ModuleRoute(ConfigHomePage.routeName, module: ConfigModule()),
    ],),
    ChildRoute(DebtorDetailPage.routeName, child: (_, __) => DebtorDetailPage(debtor: __.data)),
  ];
}