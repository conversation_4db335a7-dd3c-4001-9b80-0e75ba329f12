import 'dart:convert';
import 'dart:io';

import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_archive/flutter_archive.dart';
import 'package:share_plus/share_plus.dart';
import 'package:cross_file/cross_file.dart';
import 'package:permission_handler/permission_handler.dart';

import '../constants.dart';
import '../extensions.dart';
import '../models/debtor_model.dart';
import '../models/operation_model.dart';
import 'database/database_hive_api.dart';
import 'image_file_backup.dart';

class CSVSaver {
  static CSVSaver? _instance;

  static CSVSaver get instance {
    return _instance ??= CSVSaver();
  }

  makeDebtorBackup(DebtorObject debtor, List<OperationObject> debtorHistoric) async {
    var csvData = _convertDebtorToCsvData(debtor, debtorHistoric);
    HiveDb.putDebtorBackup(debtor.identifier, csvData);
  }

  Future<File> exportDebtorData(String archiveName, DebtorObject debtor) async {
    var csvData = HiveDb.getBackup(debtor.identifier);

    File debtorFile = await _saveCSVFile(
        "${Constants.tempFolder}/individual/${archiveName.withoutDiacriticalMarks}.csv",
        csvData
    );
    File? imageFile = ImageSaver.retrieveImageFile(debtor.fileName)
        ?.copySync("${Constants.tempFolder}/individual/${debtor.fileName}.jpg");

    var zipIndividual = await _zipBackupIndividual(debtorFile, imageFile);
    zipIndividual.copySync("${Constants.backupFolder}/individual/${zipIndividual.name}");

    CSVSaver.instance._exportFile(zipIndividual);
    return (debtorFile);
  }

  /*Future<File> generateCsvForDebtor({required String cleanFileName, required DebtorObject debtor, required debtorOperations}) async {
    String csvData = _convertDebtorToCsvData(debtor, debtorOperations);
    return _saveCSVFile("${Constants.backupFolder}/individual/$cleanFileName.csv", csvData);
  }*/

  makeGeneralBackup(List<DebtorObject> debtorList, Map<String, List<OperationObject>> operationsByDebtor) async {
    final childFolder = "geral_$TODAYm";
    for (var debtor in debtorList) {
      String csvData = _convertDebtorToCsvData(debtor, operationsByDebtor[debtor.identifier]!);
      await _saveCSVFile(
          "${Constants.generalFolder}/$childFolder/${debtor.name.withoutDiacriticalMarks.fileNameTimestamp}.csv",
          csvData);
    }

    Config.bkpGeneralFolders.add(childFolder);
    // var csvData = _convertHistoricToCsvData(historic); // await _saveCSVFile("$folder/historic.csv", csvData);
  }

  /*loadDataFromIndividual([String? otherFolder]) async {
    // await _checkPermission();
    final folder = otherFolder ?? ("${Constants.backupFolder}/individual");
    _retrieveFromFolder(folder);
  }*/

  restoreGeneralBackup() async {
    final list = Config.bkpGeneralFolders;
    if (list.isNotEmpty) {
      _retrieveFromFolder("${Constants.generalFolder}/${list.last}");
    }
  }

  /// exporta o backup geral compactado para uma pasta escolhida pelo usuário
  exportGeneralBackup([BuildContext? context]) async {
    // final box = context.findRenderObject() as RenderBox?;
    // Share.shareXFiles([bkpFile], text: "Exportar backup" /*sharePositionOrigin: box!.globalToLocal(Offset.zero) & box.size*/);
    final list = Config.bkpGeneralFolders;
    if (list.isNotEmpty) {
      final bkpFile = await _zipBackupGeneral("${Constants.generalFolder}/${list.last}");
      _exportFile(bkpFile);
    }
  }
  /// Carrega o arquivo zip de pasta externa e joga dentro da pasta interna para depois ser restaurado os dados
  importGeneralBackup() async {
    var result = await FilePicker.platform.pickFiles(type: FileType.custom, allowedExtensions: ["zip"]);
    if (result == null) return;
    final zipFile = File(result.files.first.path!);

    var parentDir = Directory(Constants.generalFolder);

    if (parentDir.existsSync()) {//todo arrumar exclusão do conteúdo completo desta pasta
      parentDir.deleteSync();
    }
    parentDir.createSync();

    ZipFile.extractToDirectory(zipFile: zipFile, destinationDir: parentDir);
  }

  /// Carrega arquivo CSV de um devedor individual
  loadDebtorFromArchive() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
        initialDirectory: Constants.backupFolder,
        allowMultiple: true,
        lockParentWindow: true,
        type: FileType.custom,
        allowedExtensions: ['csv']
    );
    if (result == null) return;

    for (var pathStr in result.paths) {
      await _restoreInDatabase(pathStr);
    }
  }

  _exportFile(File file) => Share.shareXFiles([XFile(file.path)], text: "Exportar ${file.name}");

  Future<File> _saveCSVFile(String path, String csvData) async {
    print(path);
    return await (await File(path).create(recursive: true)).writeAsString(csvData);
  }

  _retrieveFromFolder(String folder) {
    List<FileSystemEntity> csvFiles = Directory(folder).listSync(recursive: true, followLinks: false);

    for (var csv in csvFiles) {
      if (csv is File) _restoreInDatabase(csv);
    }
  }

  _restoreInDatabase(dynamic csvItem) async {
    final extracted = await _loadCSVFile((csvItem is File) ? csvItem : File(csvItem));
    final converted = _convertFromCSVData(extracted);

    _saveToDatabase(converted.first as DebtorObject, converted.last as List<OperationObject>);
  }

  _saveToDatabase(DebtorObject debtorObject, List<OperationObject> operationHistoric) {
    HiveDb.deleteDebtor(debtorObject.identifier);
    HiveDb.putDebtor(debtorObject);
    HiveDb.putDebtorHistoric(debtorObject.identifier, operationHistoric);
    // HiveDb.putBunchDebtorHistoric(debtorObject.identifier, operationHistoric);
  } //fixme: corrigir putBunchDebtorHistoric()

  Future<List<List>> _loadCSVFile(File file) async {
    final csvFile = file.openRead();
    return await csvFile
        .transform(utf8.decoder)
        .transform(const CsvToListConverter())
        .toList();
  }

  Set<Object> _convertFromCSVData(List<List<dynamic>> data) {
    final String identifier = data[0][0].toString();
    var sum = .0;
    List<OperationObject> operations = [];
    for (int i = 1; i < data.length; i++) {
      final number = double.parse(data[i][2].toString());
      sum += number;
      operations.add(OperationObject(
          debtorID: identifier,
          date: data[i][0].toString(),
          description: data[i][1].toString(),
          value: number,
          timestamp: int.parse(data[i][3].toString())
      ));
    }
    var debtor = DebtorObject(
        id: 0,
        identifier: identifier,
        name: data[0][1].toString(),
        phone: data[0][2].toString(),
        value: sum
    );
    return {debtor, operations};
  }

  String _convertDebtorToCsvData(debtor, List<OperationObject> operations) {
    final data = [
      [debtor.identifier, debtor.name, debtor.phone],
      for (int i = 0; i < operations.length; i++)
        [operations[i].date, operations[i].description, operations[i].value.toString(), operations[i].timestamp],
    ];

    return const ListToCsvConverter().convert(data);
  }

  /*String _convertHistoricToCsvData(List<OperationObject> operations) {
    final data = [
      for (OperationObject operation in operations)
        [operation.date, operation.debtorID, operation.description, operation.value.toString()],
    ];
    return const ListToCsvConverter().convert(data);
  }*/

  /*Future<XFile> _zipBackupIndividual() async {
    var directory = Directory(Constants.backupFolder);
    final zipFile = File("${Constants.tempFolder}/backupIndividual.zip");
    await zipFile.create(recursive: true);

    await ZipFile.createFromDirectory(sourceDir: directory, zipFile: zipFile, includeBaseDirectory: true);

    return XFile(zipFile.path);
  }*/

  Future<File> _zipBackupGeneral(String path) async {
    final zipFile = File("${Constants.tempFolder}/backupGeneral.zip");
    await zipFile.create(recursive: true);

    await ZipFile.createFromDirectory(sourceDir: Directory(path), zipFile: zipFile, includeBaseDirectory: true);
    return (zipFile);
  }

  Future<File> _zipBackupIndividual(File csvFile, File? imageFile) async {
    final zipFile = File("${Constants.tempFolder}/backupIndividual.zip");
    await zipFile.create(recursive: true);

    var files = (imageFile != null) ? [csvFile, imageFile,] : [ csvFile ];

    await ZipFile.createFromFiles(
      sourceDir: Directory("${Constants.tempFolder}/individual"),
      files: files,
      zipFile: zipFile,
    );
    return (zipFile);
  }

  _checkPermission() async => await Permission.storage.request();
}