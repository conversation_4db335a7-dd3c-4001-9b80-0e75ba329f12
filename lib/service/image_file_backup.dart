import 'dart:io' show File, Directory;

import 'package:flutter/material.dart';

import '../constants.dart';
import '../extensions.dart';

abstract class ImageSaver {
  static final parentFolder = Constants.imagesFolder;

  static Future<File> saveImage(File image, String fileName) async {
    var parentDir = Directory(parentFolder);
    parentDir.createSync();

    return await image.copy("$parentFolder/$fileName.jpg");
  }

  static Image? retrieveImage(String fileName)  {
    var imageFile = retrieveImageFile(fileName);
    return imageFile?.let((that) => Image.file(that),);
  }

  static File? retrieveImageFile(String fileName) {
    var parentDir = Directory(parentFolder);
    if (parentDir.existsSync()) {
      var img = File("$parentFolder/$fileName.jpg");
      if ( img.existsSync()) return img;
    }
    return null;
  }
}