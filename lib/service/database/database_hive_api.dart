import 'dart:io';
import 'package:flutter/foundation.dart' show ValueListenable;
import 'package:hive_flutter/hive_flutter.dart' show Box, BoxX, Hive, HiveX;
import 'package:path_provider/path_provider.dart' show getApplicationDocumentsDirectory, getExternalStorageDirectory;
import 'package:collection/collection.dart';

import '../../constants.dart';
import '../../models/debtor_model.dart';
import '../../models/operation_model.dart';

class HiveDb {
  HiveDb._();

  static const main_database = 'fiado_database';
  static const debtor_database = 'debtors_db';
  static const historic_database = 'operations_db';
  static const preferences_db = 'preferences_db';
  static const backup_database = 'backup_db';
  // de preferências:
  static const db_backup = 'backup_folder';
  static const _db_compact_dialog = 'backup_compact_dialog';
  static const _db_compact_layout = 'backup_compact_layout';
  static const _db_decimal_number = 'backup_decimal_number';
  static const _db_cancel_inside = 'backup_cancel_inside';
  static const _db_backup_folder = 'backup_general_folder';

  static const _db_backup_debtor = 'backup_individual_debtor';
  static const _db_backup_operations = 'backup_operations';

  static late final Box _boxPrefs;
  static late final Box _boxBackup;
  static late final Box<DebtorObject> _boxDebtor;
  static late final Box<OperationObject> _boxHistoric;

  static start() async {
    await Hive.initFlutter(main_database);
    Hive.registerAdapter(DebtorObjectAdapter());
    Hive.registerAdapter(OperationObjectAdapter());

    _boxBackup = await Hive.openBox(backup_database);
    _boxDebtor = await Hive.openBox<DebtorObject>(debtor_database);
    _boxHistoric = await Hive.openBox<OperationObject>(historic_database);
    _boxPrefs = await Hive.openBox(preferences_db);
  }

  static migration() async {
    if (await Hive.boxExists("fiado_database")) print("existe sim");
  }

  static ValueListenable<Box<DebtorObject>> listenDebtor() => _boxDebtor.listenable();
  static listenHistoric() => _boxHistoric.listenable();

  static List<DebtorObject> fetchDebtors([Box<dynamic>? outBox]) => _boxDebtor.values.toList();
  static Map<String, DebtorObject> fetchDebtorsMap() =>
      { for (var debtor in fetchDebtors()) debtor.identifier : debtor };

  static List<OperationObject> fetchHistoric([Box<dynamic>? outBox]) {
    return _boxHistoric.values.toList();
  }

  static List<OperationObject> fetchHistoricByDebtor(String debtorId, [List<OperationObject>? historic]) {
    List<OperationObject> historicAll = historic ?? fetchHistoric();
    return historicAll.where((element) => element.debtorID == debtorId).toList();
  }

  static void putDebtor(DebtorObject debtor) {
    _boxDebtor.put(debtor.identifier, debtor);
   /* List<DebtorObject> old = HiveDb.fetchDebtors(outBox);
    old.removeWhere((item) => item.identifier == debtor.identifier); // caso já exista retira antes para evitar duplicata
    (outBox ?? boxMain).put(debtor_database, old..add(debtor));*/
  }

  static void putHistoric(OperationObject operation, [Box? outBox]) {
    _boxHistoric.add(operation); // adiciona modo id numérico
  }

  static void putDebtorHistoric(String identifier, List<OperationObject> operations, [Box? outBox]) {//fixme remover função
    _boxHistoric.addAll(operations);
    // List<OperationObject> historic = HiveDb.fetchHistoricByDebtor(identifier);
    // _boxHistoric.put(identifier, operation);
  }

  /*static void putBunchDebtorHistoric(String identifier, List<OperationObject> operations) {
    for (var element in operations) { putDebtorHistoric(identifier, element); }
  }*/

  static void editDebtorHistoric(String identifier, List<OperationObject> historic) => _boxPrefs.put(identifier, historic);

  /// apaga o registor da lista de Pessoas e também o histórico correspondente
  static void deleteDebtor(String identifier) {
    _boxDebtor.delete(identifier);
    var indexesToDelete = _boxHistoric.values.toList().foldIndexed<List<int>>([], (index, previous, element) {
      if (element.debtorID == identifier) return previous..add(index);
      return previous;
    });
    for (var del in indexesToDelete.reversed) {
      _boxHistoric.deleteAt(del);
    }
  }

  static bool backupExists(String key) => _boxBackup.containsKey(key);
  static String getBackup(String key) => _boxBackup.get(key);

  static void putDebtorBackup(String identifier, String csvData) {
    _boxBackup.put(identifier, csvData);
  }

  // PREFERENCES AREA

  static loadPreferences() async {
    // Use Documents directory for external storage
    var documentsDir = await getApplicationDocumentsDirectory();
    var externalFolder = "${documentsDir.path}/FIADO";
    var internalFolder = (await getExternalStorageDirectory())!.path;

    Constants.backupFolder = "$externalFolder/$innerBkpFolder";
    Constants.tempFolder = "$internalFolder/temp";
    Constants.imagesFolder = "$internalFolder/images";
    Constants.generalFolder = "$internalFolder/general";

    Config.compactDialog = _boxPrefs.get(_db_compact_dialog, defaultValue: Config.compactDialog);
    Config.compactLayout = _boxPrefs.get(_db_compact_layout, defaultValue: Config.compactLayout);
    Config.decimalValue = _boxPrefs.get(_db_decimal_number, defaultValue: Config.decimalValue);
    Config.isCancelOut = _boxPrefs.get(_db_cancel_inside, defaultValue: Config.isCancelOut);
  }

  static Future<void> setPreference(Preference opt, dynamic value) async {
    if (opt == Preference.CMPT_LYT) return await _boxPrefs.put(_db_compact_layout, value);
    if (opt == Preference.CMPT_DLG) {
      Config.compactDialog = value;
      return await _boxPrefs.put(_db_compact_dialog, value);
    }
    if (opt == Preference.decimal) {
      Config.decimalValue = value;
      return await _boxPrefs.put(_db_decimal_number, value);
    }
    if (opt == Preference.cancelIn) {
      Config.isCancelOut = value;
      return await _boxPrefs.put(_db_cancel_inside, value);
    }
    if (opt == Preference.BKP_FOLDERS) {
      if (!Config.bkpGeneralFolders.contains(value)) {
        Config.bkpGeneralFolders.add(value);
        await _boxPrefs.put(_db_backup_folder, Config.bkpGeneralFolders);
      }
    }
  }
}