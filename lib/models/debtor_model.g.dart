// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'debtor_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DebtorObjectAdapter extends TypeAdapter<DebtorObject> {
  @override
  final int typeId = 1;

  @override
  DebtorObject read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DebtorObject(
      id: fields[0] as int,
      name: fields[1] == null ? '' : fields[1] as String,
      phone: fields[2] == null ? '' : fields[2] as String,
      value: fields[3] == null ? 0.0 : fields[3] as double,
      identifier: fields[4] as String,
    );
  }

  @override
  void write(BinaryWriter writer, DebtorObject obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.phone)
      ..writeByte(3)
      ..write(obj.value)
      ..writeByte(4)
      ..write(obj.identifier);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DebtorObjectAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
