import 'package:hive/hive.dart';
import '../extensions.dart';

part 'operation_model.g.dart';

@HiveType(typeId: 2)
class OperationObject extends HiveObject with Comparable<OperationObject> {
  @HiveField(0)
  String debtorID;

  @HiveField(1, defaultValue: '')
  String date;

  @HiveField(2)
  String description;

  @HiveField(3)
  double value;

  @HiveField(4)
  late int timestamp;

  OperationObject({required this.debtorID, required this.date, required this.description, required this.value, timestamp}) {
    this.timestamp = timestamp ?? _duration;
  }

  @override
  int compareTo(OperationObject other) {
    final otherSplit = other.date.split('/');
    final split = date.split("/");
    if (split[2].equals(otherSplit[2])) {
      if (split[1].equals(otherSplit[1])) {

        if (split[0].equals(otherSplit[0])) {
          return timestamp.compareTo(other.timestamp);
        } else {
          return split[0].compareTo(otherSplit[0]);
        }

      } else {
        return split[1].compareTo(otherSplit[1]);
      }
    } else {
      return split[2].compareTo(otherSplit[2]);
    }
  }

  get _duration => DateTime.now().millisecondsSinceEpoch;
  get day => date.split('/').first;
}