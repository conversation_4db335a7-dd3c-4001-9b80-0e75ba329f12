import 'package:controle_fiado_app/extensions.dart';
import 'package:hive/hive.dart';

part 'debtor_model.g.dart';

@HiveType(typeId: 1)
class DebtorObject extends HiveObject {
  @HiveField(0)
  int id = 0;

  @HiveField(1, defaultValue: '')
  String name = '';

  @HiveField(2, defaultValue: '')
  String phone = '';
  @HiveField(3, defaultValue: 0.0)
  double value = .0;
  @HiveField(4)
  String _identifier = '0000';

  String get fileName => "${name.fileNameSimple}_$identifier";

  set identifier(value) {
    if (_identifier == '0000') _identifier = value;
  }
  String get identifier => _identifier;

  DebtorObject.empty();
  DebtorObject(
      {required this.id,
      required this.name,
      required this.phone,
      required this.value,
      required identifier}
  ) {
    _identifier = identifier;
  }

  get valueStr => value.real;
  get valueStrAbs => value.abs().real;

  @override
  String toString() => "Debtor: $name";
}