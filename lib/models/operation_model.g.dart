// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'operation_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class OperationObjectAdapter extends TypeAdapter<OperationObject> {
  @override
  final int typeId = 2;

  @override
  OperationObject read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OperationObject(
      debtorID : fields[0] as String,
      date: fields[1] == null ? '' : fields[1] as String,
      description: fields[2] as String,
      value: fields[3] as double,
      timestamp: fields[4] as int?
    );
  }

  @override
  void write(BinaryWriter writer, OperationObject obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.debtorID)
      ..writeByte(1)
      ..write(obj.date)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.value);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OperationObjectAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}