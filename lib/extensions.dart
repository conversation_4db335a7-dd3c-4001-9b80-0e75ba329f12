import 'dart:io';
import 'dart:math';

import 'package:format/format.dart';

import 'constants.dart';

extension ObjectExt<T> on T {
  R let<R>(R Function(T that) op) => op(this);
}

extension Converting on double {
  String get real => 'R\$ ${'{:.2f}'.format(this)}';
  String get dollar => '\$ ${'{:.2f}'.format(this)}';
}

extension Utility on String {
  bool equals(String other) => compareTo(other) == 0;

  String get fileNameTimestamp {
    var splitName = split(" ");
    return "${fileNameSimple}_$TODAYmin";
      splitName.length <= 1
        ? "${splitName[0]}_$TODAYmin".toLowerCase()
        : "${splitName[0]}.${splitName[1]}_$TODAYmin".toLowerCase();
  }

  String get fileNameSimple {
    var splitName = split(" ");
    return (splitName.length <= 1
            ? splitName[0]
            : "${splitName[0]}.${splitName[1]}").toLowerCase();
  }
}
/// source: https://stackoverflow.com/a/65461381/3443949
extension DiacriticsAwareString on String {
  static const diacritics =
      'ÀÁÂÃÄÅàáâãäåÒÓÔÕÕÖØòóôõöøÈÉÊËĚèéêëěðČÇçčÐĎďÌÍÎÏìíîïĽľÙÚÛÜŮùúûüůŇÑñňŘřŠšŤťŸÝÿýŽž';
  static const nonDiacritics =
      'AAAAAAaaaaaaOOOOOOOooooooEEEEEeeeeeeCCccDDdIIIIiiiiLlUUUUUuuuuuNNnnRrSsTtYYyyZz';

  String get withoutDiacriticalMarks => splitMapJoin('',
      onNonMatch: (char) => char.isNotEmpty && diacritics.contains(char)
          ? nonDiacritics[diacritics.indexOf(char)]
          : char);
}

extension MapIndex<T> on List<T> {
  List<R> mapIndexed<R> (R Function(T element, int idx) callback) {
    List<R> result = [];

    for (int i = 0; i < length; i++) {
      R item = callback(this[i], i);
      result.add(item);
    }

    return result;
  }
}

extension FileExtension on FileSystemEntity {
  String get name => path.split('/').last;
}

extension ChangeMap on Map {
  void replaceAll<K, V> (Map<K, V> other) {
    clear();
    addAll(other);
  }
}

String get generateFourIdentifier =>
    '${Random().nextInt(10)}${Random().nextInt(10)}'
    '${Random().nextInt(10)}${Random().nextInt(10)}';
