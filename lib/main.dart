import 'package:custom_navigation_bar/custom_navigation_bar.dart';
// Removed device_preview import
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:line_icons/line_icons.dart';

import 'app_module.dart';
import 'features/module_start/pages/historic_page.dart';
import 'features/module_people/people_page.dart';
import 'features/module_start/submodule_config/pages/config_home_page.dart';
import 'service/database/database_hive_api.dart';

/// comandos para usar no terminal:
///
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await HiveDb.start();
  // await HiveDb.migration();
  await HiveDb.loadPreferences();
  runApp(ModularApp(
    module: AppModule(),
    child: const MyApp(),
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: "Seu Fiado",
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      routeInformationParser: Modular.routeInformationParser,
      routerDelegate: Modular.routerDelegate,
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({Key? key}) : super(key: key);

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _currentIndex = 0;

  final pages = [
    const DebtorsPage(),
    const HistoricPage(),
    const ConfigHomePage(),
  ];

  @override
  Widget build(BuildContext context) => SafeArea(
      child: Scaffold(
        body: pages.elementAt(_currentIndex),
        bottomNavigationBar: _customNavBar(),
      )
  );

  _customNavBar() {
    return SizedBox(
      height: 50,
      child: CustomNavigationBar(
          iconSize: 25.0,
          selectedColor: Colors.white,
          strokeColor: Colors.white70,
          unSelectedColor: const Color(0xffacacac),
          backgroundColor: Colors.blueAccent,
          borderRadius: const Radius.circular(10),
          items: [
            CustomNavigationBarItem(icon: const Icon(LineIcons.user)),
            CustomNavigationBarItem(icon: const Icon(LineIcons.receipt)),
            CustomNavigationBarItem(icon: const Icon(Icons.settings_applications_outlined))
          ],
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
      ),
    );
  }
}
