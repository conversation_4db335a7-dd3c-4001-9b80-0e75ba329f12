import 'dart:async' show Completer;
import 'dart:io' show File;
import 'dart:math' show min;
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

Future<Widget> buildCustomPaint(File img) async {
  ui.Image image = await _loadImage(img);

  return CustomPaint(painter: _PngImagePainter(image: image),);
}
/// src: https://stackoverflow.com/a/56049414/3443949
Future<ui.Image> _loadImage(File img) async {
  final ByteData data = ByteData.view(img.readAsBytesSync().buffer); /*await rootBundle.load('images/eu.jpg');*///Uint8List.fromList(await img.openRead().first).buffer.asByteData();
  final Completer<ui.Image> completer = Completer();

  ui.decodeImageFromList(Uint8List.view(data.buffer), (ui.Image ig) {
    return completer.complete(ig);
  });
  return completer.future;
}

class _PngImagePainter extends CustomPainter {
  _PngImagePainter({required this.image,});
  ui.Image image;

  @override
  void paint(Canvas canvas, Size size) {
    _drawCanvas(size, canvas);
    // _saveCanvas(size);
  }

  Canvas _drawCanvas(Size size, Canvas canvas) {
    const center = Offset(150, 50);
    final radius = min(size.width, size.height) / 8;

    // The circle should be paint before or it will be hidden by the path
    Paint paintCircle = Paint()..color = Colors.black;
    Paint paintBorder = Paint()
      ..color = Colors.white
      ..strokeWidth = size.width / 36
      ..style = PaintingStyle.stroke;
    canvas.drawCircle(center, radius, paintCircle);
    canvas.drawCircle(center, radius, paintBorder);

    double drawImageWidth = -80;
    var drawImageHeight = -size.height * 0.1;

    Path path = Path()
      ..addOval(Rect.fromLTWH(size.width / 4, drawImageHeight,
          image.width.toDouble() / 2, image.width.toDouble() / 2));

    canvas.clipPath(path);

    canvas.drawImage(image, Offset(drawImageWidth, drawImageHeight), Paint());
    return canvas;
  }

  /*_saveCanvas(Size size) async {
    var pictureRecorder = ui.PictureRecorder();
    var canvas = Canvas(pictureRecorder);
    var paint = Paint();
    paint.isAntiAlias = true;

    _drawCanvas(size, canvas);

    var pic = pictureRecorder.endRecording();
    ui.Image img = await pic.toImage(image.width, image.height);
    var byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    var buffer = byteData!.buffer.asUint8List();

    // var response = await get(imgUrl);
    var documentDirectory = await getApplicationDocumentsDirectory();
    File file = File(join(documentDirectory.path,
        '${DateTime.now().toUtc().toIso8601String()}.png'));
    file.writeAsBytesSync(buffer);

    print(file.path);
  }*/

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}