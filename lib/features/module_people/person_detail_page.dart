import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart' show ScrollDirection;
import 'package:hive_flutter/hive_flutter.dart' show Box;
import 'package:line_icons/line_icons.dart';
import 'package:supercharged/supercharged.dart' show IntSC;
import 'package:toast/toast.dart' show Toast, ToastContext;
// ignore: import_of_legacy_library_into_null_safe
import 'package:whatsapp_share/whatsapp_share.dart' show WhatsappShare;

import '../../colors.dart';
import '../../constants.dart';
import '../../extensions.dart';
import '../../models/debtor_model.dart';
import '../../models/operation_model.dart';
import '../../service/csv_file_backup.dart';
import '../../service/database/database_hive_api.dart';
import '../widgets/dialogs.dart';
import '../widgets/extensions_widgets.dart';
import '../widgets/extra_widgets.dart';
import '../widgets/search_delegate.dart';

part 'person_detail_page.functions.dart';

/// refs: https://stackoverflow.com/a/59408336/3443949
/// https://stackoverflow.com/questions/53372276/flutter-how-to-check-if-sliver-appbar-is-expanded-or-collapsed
class DebtorDetailPage extends StatefulWidget {
  const DebtorDetailPage({super.key, required this.debtor});
  final DebtorObject debtor;

  static const routeName = '/detailedDebtor';

  @override
  State<DebtorDetailPage> createState() => _DebtorDetailPageState();
}
/// melhorar código:
class _DebtorDetailPageState extends State<DebtorDetailPage> with SingleTickerProviderStateMixin<DebtorDetailPage> {
  var debtorHistoric = <OperationObject>[];
  late AnimationController _hideFabAnim;
  late Animation<Offset> _animSlide;
  late Animation<double> _animOpacity;

  @override
  void initState() {
    super.initState();
    _hideFabAnim = AnimationController(vsync: this, duration: 500.milliseconds);
    _animSlide = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0, 2),
    ).animate(
      // _hideFabAnim,
      CurvedAnimation(
        parent: _hideFabAnim,
        curve: Curves.easeInCubic,
      ),
    );
    _animOpacity = Tween<double>(begin: 1.0, end: 0.0).animate(_hideFabAnim);
  }

  @override
  void dispose() {
    _hideFabAnim.dispose();
    super.dispose();
  }

  bool _handleScrollNotification(UserScrollNotification userScroll) {
    switch (userScroll.direction) {
      case ScrollDirection.forward: _hideFabAnim.reverse();
        break;
      case ScrollDirection.reverse: _hideFabAnim.forward();
        break;
      case ScrollDirection.idle:
        break;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
          floatingActionButton: SlideTransition(
            position: _animSlide,

            child:  FadeTransition(
              opacity: _animOpacity,

              child: FloatingActionButton(
                onPressed: () => _openOperationDialog(widget.debtor, null),
                child: const Icon(Icons.add_circle_outlined),
              ),
            ),
          ),
          backgroundColor: Colors.white,

          body: NestedScrollView(
              headerSliverBuilder: (_, innerScrolled) => [
                  SliverLayoutBuilder(
                    builder: (context, constraints) {
                      final scrolled = constraints.scrollOffset > 130;
                      return SliverAppBar(
                          floating: false,
                          pinned: true,
                          expandedHeight: 200,
                          forceElevated: innerScrolled,
                          shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(30),
                                  bottomRight: Radius.circular(30))),
                          leading: IconButton(
                              onPressed: () => Navigator.pop(context),
                              icon: const Icon(Icons.arrow_back_rounded)),
                          flexibleSpace: FlexibleSpaceBar(
                            centerTitle: true,
                            collapseMode: CollapseMode.pin,
                            title: Text(widget.debtor.name, style: TextStyle(color: scrolled ? Colors.white : Colors.transparent)),
                            background: _headBackground(widget.debtor, context),
                          ),
                      );
                    },
                  )
                ],
            body: ValueListenableBuilder<Box<OperationObject>>(
              valueListenable: HiveDb.listenHistoric(),
              builder: (ctx, box, _) {
                ToastContext().init(ctx);
                return NotificationListener<UserScrollNotification>(
                  onNotification: _handleScrollNotification,
                  child: _bodyBuilder(box, widget.debtor),
                );
          },
        ),
      ),
    ));
  }

  /// Appear when the head is expanded
  Widget _headBackground(DebtorObject debtor, BuildContext context) {
    return SizedBox.expand(
      child: Container(
        color: MyColor.primary,
        child: Stack(
          children: [
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(debtor.name, style: const TextStyle(fontSize: 25, color: Colors.white)),
                  const SizedBox(height: 15),
                  Text(
                      (debtor.value > 0) ? "está devendo:" : "tem crédito de:",
                      style: const TextStyle(color: Colors.white)
                  ), // TEXTO PARA VALOR
                  Text(
                    debtor.valueStrAbs,
                    style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 30
                    ),
                  ) // VALOR TOTAL
                ],
              ),
            ), // DETALHES CENTRAIS

            Container(
              padding: const EdgeInsets.only(top: 10, right: 8),
              alignment: Alignment.topRight,
              child: PopupMenuButton(
                icon: const Icon(Icons.more_vert_outlined, color: Colors.white,),
                padding: const EdgeInsets.all(.2),
                onSelected: (option) {
                  switch (option) {
                    case 0: _onSaveBackup(debtor);
                      break;
                    case 1: _onExportBackup(debtor); // abrir uma caixa de diálogo para compartilhar os dados
                      break;
                    case 2: _onShareWhatsApp(debtor);
                      break;
                    case 3: _onMakeZero(debtor);
                      break;
                  }
                },
                itemBuilder: (_) =>
                    [
                      _OptionsForDetail(LineIcons.saveAlt, "Salvar"),
                      _OptionsForDetail(LineIcons.excelFile, "Exportar"),
                      _OptionsForDetail(LineIcons.whatSAppSquare, "Compartilhar"),
                      _OptionsForDetail(Icons.call_received_outlined, "Zerar")
                    ]
                    .mapIndexed((item, idx) => PopupMenuItem(
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        value: idx,
                        child: Row(
                          children: [
                            const SizedBox(width: 5), Icon(item.icon, size: 15),
                            const SizedBox(width: 5), Text(item.option, style: const TextStyle(fontSize: 15))
                          ],
                        )
                    )),
              ),
            ), // BOTÃO OPÇÕES

            Container(
              padding: const EdgeInsets.only(bottom: 5, right: 8),
              alignment: Alignment.bottomRight,
              child: CircularIconButton(
                  LineIcons.edit,
                  onPressed: () => _openDebtorDialog(context, debtor)
              ),
            ), // BOTÃO EDIÇÃO

            Container(
                padding: const EdgeInsets.only(left: 5, bottom: 5),
                alignment: Alignment.bottomLeft,
                child: CircularIconButton(
                    onPressed: () => _onMakeSearch(context),
                    Icons.search
                )
            ) // BOTÃO PESQUISA
          ],
        ),
      ),
    );
  }

  Widget _bodyBuilder(Box<OperationObject> box, DebtorObject debtor) {
    if (box.isEmpty) return Container();

    debtorHistoric = HiveDb.fetchHistoricByDebtor(debtor.identifier);
    debtorHistoric.sort();

    _assertDebtorValue(debtor);
    return Container(
      color: MyColor.primary,
      child: Container(
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30),
                topRight: Radius.circular(30)
            )
        ),
        child: ListView.builder(
            // primary: false,
            // controller: scrollController,
            itemCount: debtorHistoric.length,
            itemBuilder: (_, int idx) {
              final currentItem = debtorHistoric[idx];

              return InkWell(
                onTap: () => _openOperationDialog(debtor, currentItem),
                child: Card(
                  elevation: 2,
                  color: (currentItem.value < .0) ? Colors.red[100] : Colors.green[100],
                  margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                  shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10))),
                  // COMPONENTES DA LINHA:
                  child: _lineOfOperationWidget(currentItem),
                ),
              );

            }
        ).paddingAll(12),
      ),
    );
  }

  Widget _lineOfOperationWidget(OperationObject currentItem) {
    bool big = currentItem.value >= 100;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(flex: 35, child: Text(currentItem.date)),
        Expanded(flex: big ? 39 : 45, child: Text(currentItem.description)),
        Expanded(flex: big ? 26: 20, child: Text(currentItem.value.real, textAlign: TextAlign.end,)),
      ],
    ).paddingSymmetric(horizontal: 8, vertical: 5);
  }

  _openDebtorDialog(BuildContext context, DebtorObject debtor) async {
    final String? result = await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => PersonDialog(debtor)
    );
    if (result != null && result.equals("delete")) {
      HiveDb.deleteDebtor(debtor.identifier); // o registro geral permanece (por enquanto)
      Toast.show("Registro de ${debtor.name} apagado", duration: Toast.lengthLong, gravity: Toast.bottom);
      if (!mounted) return;
      Navigator.pop(context);
    }
  }

  _openOperationDialog(DebtorObject debtor, OperationObject? operation) async {
    final oldOperation = operation;

    final OperationObject? result = await showDialog<OperationObject>(
        context: context,
        barrierDismissible: Config.isCancelOut,
        builder: (_) => OperationDialog(oldOperation, debtor: debtor)
    );

    if (result != null) { // atualizar novos dados
      var newValue = debtor.value - oldOperation!.value; // resultado existe
      newValue += result.value;
      debtor.value = newValue;

      oldOperation
        ..value = result.value
        ..date = result.date
        ..debtorID = result.debtorID
        ..description = result.description
        ..timestamp = result.timestamp;

      debtor.save();
      oldOperation.save();
    }
  }

  _onMakeSearch(BuildContext context) async {
    final result = await showSearch(context: context, delegate: DebtorSearchDelegate());
    if (!mounted) return;
    if (result == null) return; // se não buscou não atualiza página
    Navigator.popAndPushNamed(context, DebtorDetailPage.routeName, arguments: result);
  }

  _onMakeZero(DebtorObject debtor) {
    final double diff = (.0 - debtor.value);
    var operationObject = OperationObject(
        debtorID: debtor.identifier,
        date: TODAY,
        description: "abate total",
        value: diff
    );

    setState(() => debtor.value = .0);
    HiveDb.putDebtor(debtor);
    HiveDb.putHistoric(operationObject);
  }

  _onSaveBackup(DebtorObject debtor) async {
    await CSVSaver.instance.makeDebtorBackup(debtor, debtorHistoric);
  }

  _onExportBackup(DebtorObject debtor) async {
    if (!HiveDb.backupExists(debtor.identifier)) {
      await _onSaveBackup(debtor);
    }

    String? result = await showDialog(
        barrierLabel: "TITLE",
        context: context,
        builder: (_) => BackupDialog(debtor: debtor, debtorOperations: debtorHistoric)
    );

    // salvar com o nome de arquivo em 'result'
    if (result != null) {
      File file = await CSVSaver.instance.exportDebtorData(result, debtor);
      Toast.show("Backup feito: ${file.name}", duration: Toast.lengthShort);
    }
  }

  /// corrige o valor total do devedor somando os valores das operações do mesmo
  void _assertDebtorValue(debtor) async {
    double fold = debtorHistoric.fold(0.0, (previous, item) => item.value + previous);
    if (fold != debtor.value) {
      debtor.value = fold;
      await debtor.save();
      setState(() {});
    }
  }
}

class _OptionsForDetail {
  _OptionsForDetail(this.icon, this.option);
  final IconData icon;
  final String option;
}