part of 'person_detail_page.dart';

_onShareWhatsApp(DebtorObject debtor) async {
  /*var encodeContent = Uri.encodeComponent(
        "Você está ${debtor.value < 0 ? "com crédito de" : "devendo"} ${debtor.valueStr} no estabelecimento [nome]");
    var uri = _whatsappURL(_cleanNumber(debtor.phone), encodeContent);
    print(uri);*/
  //"https://whatsa.me/${_cleanNumber(debtor.phone)}/?t=${Uri.encodeComponent(content)}";
  await WhatsappShare.share(
      phone: _cleanNumber(debtor.phone),
      text: "Você está ${debtor.value < 0 ? "com crédito de" : "devendo"} "
          "${debtor.valueStr} no estabelecimento ${SystemValues.shopName}"
  );
  // await launch((uri));
}

// ignore: prefer_interpolation_to_compose_strings
String _cleanNumber(content) => "55" + content.replaceAll(RegExp(r'[^\w\\s]+'), '');
// String _whatsappURL(String phone, String content) => "https://api.whatsapp.com/send?phone=$phone\&text=$content";