// ignore_for_file: non_constant_identifier_names

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:hive_flutter/adapters.dart' show Box, BoxX;
import 'package:image_cropper/image_cropper.dart' show AndroidUiSettings, CropAspectRatio, ImageCompressFormat, ImageCropper;
import 'package:image_picker/image_picker.dart' show ImagePicker, ImageSource, XFile;
import 'package:line_icons/line_icons.dart';
import 'package:supercharged/supercharged.dart';

import '../../constants.dart';
import '../../extensions.dart';
import '../../models/debtor_model.dart';
import '../../service/database/database_hive_api.dart';
import '../../service/image_file_backup.dart';
import '../widgets/dialogs.dart';
import '../widgets/extensions_widgets.dart';
import '../widgets/extra_widgets.dart';
import 'person_detail_page.dart';

enum DataFilter {
  <PERSON><PERSON>('A-Z'), <PERSON><PERSON>('Z-A'), DOWNWARD('VALOR [maior-menor]'), UPWARDS("VALOR [menor-maior]");

  final String option;
  const DataFilter(this.option);
}

class DebtorsPage extends StatefulWidget {
  const DebtorsPage({Key? key}) : super(key: key);

  @override
  State<DebtorsPage> createState() => _DebtorsPageState();
}

class _DebtorsPageState extends State<DebtorsPage> {
  /*final List<DebtorObject> _elements = [
    DebtorObject(id: 0, name: "Solange Cristina", phone: "(98) 98436-1247", value: 12.5, identifier: '0123'),
    DebtorObject(id: 1, name: "Marcela Freitas", phone: "(98) 0000-2345", value: 7.75, identifier: '0234'),
    DebtorObject(id: 2, name: "Ricardo Augusto", phone: "(99) 1007-2345", value: 5.0, identifier: '4523'),
    DebtorObject(id: 3, name: "Maria Rita", phone: "(99) 1007-2345", value: 10.0, identifier: '7886'),
    DebtorObject(id: 4, name: "Sofia Nogueira", phone: "(99) 4457-1234", value: 23.5, identifier: '1459'),
  ];*/
  var filter = DataFilter.AZ;
  var _isCompact = false;
  var _isSearching = false;
  var _searchText = "";
  var _verticalPadd = 8.0;
  var _showValue = true;

  @override
  void initState() {
    _isCompact = Config.compactLayout;
    super.initState();
  }

  void _changeFilter(DataFilter value) => setState(() => filter = value);

  void _changeModeCompact() async {
    _isCompact = !_isCompact;
    await HiveDb.setPreference(Preference.CMPT_LYT, _isCompact);
    setState(() {
      _verticalPadd = _isCompact ? 1.0 : 8.0;
    });
  }

  void switchSearching(bool isActivated) => setState(() { _isSearching = isActivated; });

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      floatingActionButton: FloatingActionButton(
          child: const Icon(Icons.add),
          onPressed: () => opens(context)
      ),
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.blue[600],
        title: (_isSearching)
            ? Container(
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(5)
                ),
                child: Center(
                  child: TextField(
                    onChanged: (text) => setState(() => _searchText = text),
                    decoration: InputDecoration(
                        border: InputBorder.none,
                        // isDense: true,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                        suffixIcon: IconButton(
                            onPressed: () => turnOffSearch(),
                            icon: const Icon(LineIcons.stopCircle)
                        ),
                      hintText: "Buscar por nome.."
                    ),
                    maxLines: 1,
                    autofocus: true,
                  ),
                ),
              )
            : const Text("Devedores"),

        actions: [ // opções da barra de tarefas
          if (!_isSearching) IconButton(
              onPressed: () => switchSearching(true),
              icon: const Icon(LineIcons.search)
          ),
          const SizedBox(width: 5),

          SizedBox(width: 40,
            child: IconButton(
                iconSize: 30,
                onPressed: () => setState(() => _showValue = !_showValue),
                icon: Icon(_showValue ? LineIcons.eyeAlt : LineIcons.eyeSlashAlt),
          )),

          PopupMenuButton<dynamic>(
              icon: const Icon(LineIcons.filter),
              position: PopupMenuPosition.under,
              onSelected: (value) {
                if (value == 0) { _changeModeCompact(); }
                else { _changeFilter(value); }
              },
              itemBuilder: (_) {
                final iterable = DataFilter.values.map((e) => PopupMenuItem(value: e, child: Text(e.option)));
                return [...iterable, const PopupMenuItem(value: 0, child: Text("compacto"))];
              }
          )
        ],
      ),

      body: ValueListenableBuilder<Box<DebtorObject>>(
          valueListenable: HiveDb.listenDebtor(),
          builder: (_, box, __) {
            if (box.isNotEmpty) {
              final debtorList = box.values.toList();
              var filteredList = <DebtorObject>[];

              comparator(String searchItem, String comparedItem) =>
                  RegExp("\\b$searchItem", caseSensitive: false).hasMatch(comparedItem);

              if (_isSearching) {
                filteredList = _searchText.isNotEmpty
                  ? debtorList
                      .where((elem) => comparator(
                          _transformToFilter(_searchText.trim()),
                          _transformToFilter(elem.name)))
                      .toList()
                    : debtorList; // busca ativada, mas nada digitado ainda
              } else {
                filteredList = debtorList;
              }

              final scrollController = ScrollController(initialScrollOffset: 0); // para barra de rolagem

              return (filteredList.isNotEmpty)
                  ? Scrollbar(
                      interactive: true,
                      thickness: 20,
                      controller: scrollController, // thumbVisibility: true,
                      child: (filter == DataFilter.UPWARDS || filter == DataFilter.DOWNWARD)
                        ? LinearListView(List.from(filteredList), scrollController) // cópia para não alterar original
                        : GroupListView(filteredList, scrollController)
                  ).paddingOnly(top: 5)
                  : const Center(child: Text("NADA ENCONTRADO", style: TextStyle(fontSize: 25),));
            } else {
              return const Center(child: Text("NENHUM ITEM", style: TextStyle(fontSize: 25),));//const Center(child: CircularProgressIndicator());
            }
          }
      ),
    );
  }

  GroupedListView GroupListView(List<DebtorObject> filteredList, ScrollController scrollController) {
    return GroupedListView<DebtorObject, String>(
        elements: filteredList,
        controller: scrollController,
        useStickyGroupSeparators: true,
        floatingHeader: true,
        groupBy: (item) => _transformToFilter(item.name[0]),
        groupComparator: sortingLetter,
        itemComparator: (item1, item2) => sortingLetter(_transformToFilter(item1.name), _transformToFilter(item2.name)),
        groupSeparatorBuilder: (String groupTitle) => Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              decoration: const BoxDecoration(
                  color: Colors.blueAccent,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(20), topRight: Radius.circular(20)),
              ),

              child: Text(
                  groupTitle,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 22,
                    color: Colors.white,
                    fontWeight: FontWeight.bold
                  ),
              ).paddingSymmetric(horizontal: 10, vertical: 2),
            ),
          ],
        ),
        itemBuilder: _debtorsListItem,
    );
  }

  ListView LinearListView(List<DebtorObject> list, ScrollController scrollController) {
    list.sort((a, b) => (filter == DataFilter.UPWARDS) ? a.value.compareTo(b.value) : b.value.compareTo(a.value));
    return ListView.builder(
        controller: scrollController,
        itemCount: list.length,
        itemBuilder: (ctx, index) => _debtorsListItem(ctx, list[index])
    );
  }

  void turnOffSearch() {
    _searchText = "";
    switchSearching(false);
  }

  int sortingLetter(group1, group2) => (filter == DataFilter.AZ)
                  ? group1.compareTo(group2)
                  : group2.compareTo(group1);

  Widget _debtorsListItem(BuildContext ctx, DebtorObject element) {
      return Card(
        elevation: 8.0,
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(15))),

        child: AnimatedPadding(
          padding: EdgeInsets.symmetric(horizontal: 15.0, vertical: _verticalPadd),
          duration: 500.milliseconds,
          curve: Curves.linearToEaseOut,
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 0.2),
            title: Text(
              element.name,
              style: const TextStyle(fontSize: 16),
            ),
            subtitle: (_isCompact) ? null : Text(element.phone),
            leading: (_isCompact) ? null : CircularContainer(
              innerColor: Colors.grey.shade800,
              body: _iconDebtor(element),
            ),
            trailing: Text((!_showValue) ? "R\$  --.-- " : element.valueStr, style: const TextStyle(color: Colors.redAccent)),
            onTap: () {
              Navigator.of(ctx).pushNamed(DebtorDetailPage.routeName, arguments: element);
              turnOffSearch();
            },
          ),
        ),
      );
    }

  String _transformToFilter(String value) => value.toUpperCase().withoutDiacriticalMarks;

  _capturePhoto(DebtorObject debtor) async {
    final XFile? photo = (await ImagePicker().pickImage(source: ImageSource.camera));
    if (photo != null) {
      var cropped = await ImageCropper().cropImage(
          sourcePath: photo.path,
          aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
          maxHeight: 800,
          maxWidth: 800,
          compressFormat: ImageCompressFormat.jpg,
          uiSettings: [
            AndroidUiSettings(
                toolbarColor: Colors.deepOrange,
                toolbarWidgetColor: Colors.white,
                toolbarTitle: "Corte a foto de perfil",
                showCropGrid: true
            )
          ]
      );
      await ImageSaver.saveImage(File(cropped!.path), debtor.fileName);
      setState(() {}); // atualizar a tela com a imagem
    }
  }

  _iconDebtor(DebtorObject element) {
    var imgObj = ImageSaver.retrieveImage(element.fileName);
    if (imgObj != null) {
      return CircleAvatar(backgroundImage: imgObj.image,); // por enquanto a mudança de foto não é permita
    } else {
      return IconButton(
          onPressed: () => _capturePhoto(element),
          icon: const Icon(Icons.camera, color: Colors.white70,)
      );
    }
  }

  opens(BuildContext context, [DebtorObject? debtorObject]) {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => PersonDialog(debtorObject)
    );
  }
}