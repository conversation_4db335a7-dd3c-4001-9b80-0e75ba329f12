import 'package:flutter/material.dart';

import '../../colors.dart';
import '../../models/debtor_model.dart';

CustomScrollView buildCustomScrollView(BuildContext context, DebtorObject debtor) {
  return CustomScrollView(
    slivers: [
      SliverAppBar(
        floating: false,
        pinned: true,
        expandedHeight: 200,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30))),
        leading: IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back_rounded)),
        flexibleSpace: FlexibleSpaceBar(
          centerTitle: true,
          collapseMode: CollapseMode.pin,
          background: SizedBox.expand(
            child: Container(
              color: MyColor.primary,
              child: Center(child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(debtor.name, style: const TextStyle(fontSize: 25, color: Colors.white)),
                  const SizedBox(height: 15),
                  Text(
                      (debtor.value > 0) ? "está devendo:" : "tem crédito de:",
                      style: const TextStyle(color: Colors.white)
                  ), // TEXTO PARA VALOR
                  Text(
                    debtor.valueStrAbs,
                    style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 30
                    ),
                  ) // VALOR TOTAL
                ],
              )),
            ),
          ),
        ),
      ),

      SliverToBoxAdapter(
        child: Container(
          color: MyColor.primary,
          child: Container(
            decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30)
                )
            ),
            child: const Center(child: Text("teste"),),
          ),
        ),
      )
    ],
  );
}