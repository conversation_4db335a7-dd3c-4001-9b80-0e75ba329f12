import 'dart:io';
import 'dart:math' show pi;

import 'package:collection/collection.dart';
import 'package:controle_fiado_app/constants.dart';
import 'package:controle_fiado_app/features/widgets/config_option_checkbox_widget.dart';
import 'package:controle_fiado_app/features/widgets/config_option_text_widget.dart';
import 'package:controle_fiado_app/features/widgets/expandable_widget.dart';
import 'package:controle_fiado_app/features/widgets/extensions_widgets.dart';
import 'package:controle_fiado_app/features/widgets/extra_widgets.dart';
import 'package:controle_fiado_app/models/debtor_model.dart';
import 'package:controle_fiado_app/models/operation_model.dart';
import 'package:controle_fiado_app/service/csv_file_backup.dart';
import 'package:controle_fiado_app/service/database/database_hive_api.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:toast/toast.dart';

class ConfigHomePage extends StatelessWidget {
  const ConfigHomePage ({Key? key}) : super(key: key);

  static String routeName = '/config_page';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade200,
      appBar: AppBar(
        backgroundColor: Colors.blue[600],
        title: const Text("Configurações"),
      ),

      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _cardOptionTile(
            content: ConfigOptionTextField(
              shopName: SystemValues.shopName,
              onUpdate: (name) {
                SystemValues.shopName = name;
              },
            ),
          ),
          const SizedBox(height: 5),

          _cardOptionTile(content: ExpandableNotifier(
            child: ScrollOnExpand(
                child: ExpandablePanel(
                  theme: const ExpandableThemeData(
                      headerAlignment: ExpandablePanelHeaderAlignment.center,
                      tapHeaderToExpand: true,
                      hasIcon: false
                  ),
                  header: headerBackup(),
                  collapsed: Container(),
                  expanded: const _BodyBackup(),
                )),
          )),
          const SizedBox(height: 5),

          _cardOptionTile(
            content: ConfigOptionCheckbox(
                initialValue: Config.compactDialog,
                title: "Modo compacto ao adicionar gasto",
                onUpdate: (change) async {
                  await HiveDb.setPreference(Preference.CMPT_DLG, change);
                }),
            radius: 10,
          ),
          const SizedBox(height: 5),

          _cardOptionTile(
            content: ConfigOptionCheckbox(
                  initialValue: Config.decimalValue,
                  title: "Usar vírgula automática ao adicionar valor",
                  onUpdate: (change) async {
                    await HiveDb.setPreference(Preference.decimal, change);
                  },
              ),
          ),
          const SizedBox(height: 5),

          _cardOptionTile(
            content: ConfigOptionCheckbox(
                  initialValue: Config.isCancelOut,
                  title: "Botão grande de OK",
                  onUpdate: (change) async =>
                    await HiveDb.setPreference(Preference.cancelIn, change)),
          ),
        ],
      ).paddingAll(15),
    );
  }

  ListTile headerBackup() {
    return ListTile(
      title: const Text("Backup"),
      leading: const Icon(Icons.settings_backup_restore_outlined, size: 25,),
      trailing: CircularContainer(
          size: 30,
          innerColor: Colors.grey.shade300,
          body: ExpandableIcon(
            theme: const ExpandableThemeData(
                expandIcon: Icons.arrow_forward_outlined,
                collapseIcon: Icons.arrow_downward_outlined,
                iconRotationAngle: pi / 2,
                iconPadding: EdgeInsets.only(right: 2),
                hasIcon: false
            ),
          )
      ),
    );
  }

  Widget _cardOptionTile({required Widget content, double radius = 8}) {
    return Card(
      elevation: 5,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(radius))
      ),
      child: content,
    );
  }
}

// BACKUP AREA:
class _BodyBackup extends StatefulWidget {
  const _BodyBackup({Key? key}) : super(key: key);

  @override
  State<_BodyBackup> createState() => _BodyBackupState();
}
class _BodyBackupState extends State<_BodyBackup> {
  final controller = TextEditingController(text: Constants.backupFolder);
  var isTextEnabled = Config.defaultBkpActive;

  _changeDefault(enabled) {
    setState(() {
      isTextEnabled = enabled;
    });
    Config.defaultBkpActive = enabled;
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final List<DebtorObject> allDebtors = HiveDb.fetchDebtors();
    final allHistoric = HiveDb.fetchHistoric();
    final operationsByDebtor =
      /*allDebtors.fold<Map<String, List<OperationObject>>>({}, (previous, element) =>
        previous..putIfAbsent(element.identifier, () => HiveDb.fetchHistoricByDebtor(element.identifier, allHistoric)));*/
    groupBy(allHistoric, (OperationObject p0) => p0.debtorID);
    ToastContext().init(context);

    return Column(
      children: [
        const Divider(height: 1, thickness: .5, indent: 1, endIndent: 1, color: Colors.black),
        const SizedBox(height: 5,),

        Row(
          children: [
            Checkbox(value: isTextEnabled, onChanged: _changeDefault),
            const Text("Pasta padrão:", style: TextStyle(color: Colors.blueGrey, fontSize: 11)),
            Expanded(
              child: TextField(
                textAlign: TextAlign.center,
                controller: controller,
                enabled: !isTextEnabled,
                decoration: const InputDecoration(
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(vertical: 5)
                ),
              ), //PASTA PADRÃO
            ), //PASTA PADRÃO
          ],
        ),
        // Carregar e Salvar geral:
        SizedBox(
          height: 35,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                  onPressed: () async {
                    await CSVSaver.instance.makeGeneralBackup(allDebtors, operationsByDebtor);
                    Toast.show("Backup geral criado", duration: Toast.lengthLong, gravity: Toast.center);
                  },
                  child: const Text("Criar")
              ),
              TextButton(
                  onPressed: (Config.bkpGeneralFolders.isEmpty)
                              ? null
                              : () {
                                CSVSaver.instance.restoreGeneralBackup();
                                Toast.show("Backup restaurado", duration: Toast.lengthShort, gravity: Toast.center);
                              },
                  child: const Text("Restaurar")
              ),
              const VerticalDivider(width: 1, thickness: .5, indent: 5, endIndent: 5, color: Colors.black,),
              TextButton(
                  onPressed: () => CSVSaver.instance.exportGeneralBackup(),
                  child: const Text("Exportar zip")
              ),
              TextButton(
                  onPressed: () => CSVSaver.instance.importGeneralBackup(),
                  child: const Text("Importar zip")
              ),// const TextButton(onPressed: null, child: Text("Salvar")),
            ],
          ),
        ),

        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextButton(
                onPressed: () {
                  CSVSaver.instance.loadDebtorFromArchive();
                },
                child: const Text("Importar csv")
            ),
          ],
        )
      ],
    );
  }
}

Future<bool> _checkPermissions() async {
  if (Platform.isAndroid) {
    return (
        await _requestPermissions(Permission.storage, "storage") &&
            await _requestPermissions(Permission.accessMediaLocation, "mediaLocation") &&
            await _requestPermissions(Permission.manageExternalStorage, "external")
    );
  }
  return false;
}

Future<bool> _requestPermissions(Permission permission, String name) async {
  var status = await permission.request();
  print("STATUS ${name}: $status");

  if (status.isGranted) {
    return true;
  } else if(status.isPermanentlyDenied) {
    return false;
  } else if (status.isDenied) {
    print("Permission ${name} denied");
    return false;
  }

  return false;
}