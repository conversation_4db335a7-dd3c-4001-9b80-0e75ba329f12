import 'package:controle_fiado_app/features/module_start/submodule_config/pages/config_home_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_modular/flutter_modular.dart';

class StartController extends ValueNotifier<int> implements Disposable {
  StartController([value = 0]) : super(value);
  final pageController = PageController();

  @override
  void dispose() {
    pageController.dispose();
  }

  jumpToPage(int index) {
    pageController.jumpToPage(index);
    if (index == 2) {
      Modular.to.pushReplacementNamed(ConfigHomePage.routeName); // evitar empilhamento de chamadas
    }
    value = index;
  }
}