import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart' show Box, BoxX;

import '../../../extensions.dart';
import '../../../models/debtor_model.dart';
import '../../../models/operation_model.dart';
import '../../../service/database/database_hive_api.dart';
import '../../widgets/extra_widgets.dart' show Conditional;

class HistoricPage extends StatelessWidget {
  const HistoricPage ({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.blue[600],
        title: const Text("Histórico de operações"),
        actions: const [],
      ),
      body: ValueListenableBuilder<Box<OperationObject>>(
          valueListenable: HiveDb.listenHistoric(),
          builder: (ctx, box, widget) {
            return Conditional(
              condition: box.isNotEmpty,
              isTrue: _isTrue(box, (HiveDb.fetchDebtorsMap())),
              isFalse: const Center(child: Text("NENHUM ITEM", style: TextStyle(fontSize: 25),))
            );
          }),
    );
  }

  Widget _isTrue(Box<OperationObject> operationsBox, Map<String, DebtorObject> mapDebtors) {
    List<OperationObject> historic = operationsBox.values.toList();
    historic.sort((a, b) => b.compareTo(a)); // ordenação inversa
    /*GroupedListView(
      elements: historic,
      groupBy: (OperationObject element) { return element.day; },
    );*/

    return ListView.builder(
      itemCount: historic.length,
        itemBuilder: (_, index) {
          return Card(
            elevation: 8,
            margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(15))),
            child: Builder(builder: (_) {
                var currentItem = historic[index];
                var debtor = mapDebtors[currentItem.debtorID]!;
                return ListTile(
                  leading: Column(mainAxisAlignment: MainAxisAlignment.center, children: [Text(currentItem.date),],),
                  title: Text(debtor.name),
                  trailing: Text(currentItem.value.real, style: const TextStyle(color: Colors.redAccent)),
                );
              }
            ),
          );
        }
    );
  }
}