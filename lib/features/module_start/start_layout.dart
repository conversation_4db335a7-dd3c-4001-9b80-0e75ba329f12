import 'package:controle_fiado_app/features/module_people/people_page.dart';
import 'package:controle_fiado_app/features/module_start/pages/historic_page.dart';
import 'package:controle_fiado_app/features/module_start/start_controller.dart';

import 'package:custom_navigation_bar/custom_navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:line_icons/line_icons.dart';

class StartLayout extends StatefulWidget {
  const StartLayout({super.key});

  @override
  State<StartLayout> createState() => _StartLayoutState();
}

class _StartLayoutState extends State<StartLayout> {
  final controller = Modular.get<StartController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: controller.pageController,
        children: const [
          DebtorsPage(),
          HistoricPage(),
          RouterOutlet(), // config page
        ],
      ),
      bottomNavigationBar: _customNavBar(),
    );
  }

  _customNavBar() {
    return AnimatedBuilder(
      animation: controller.pageController,
      builder: (context, snapshot) {
        return SizedBox(
          height: 50,
          child: CustomNavigationBar(
            iconSize: 25.0,
            selectedColor: Colors.white,
            strokeColor: Colors.white70,
            unSelectedColor: const Color(0xffacacac),
            backgroundColor: Colors.blueAccent,
            borderRadius: const Radius.circular(10),
            items: [
              CustomNavigationBarItem(icon: const Icon(LineIcons.user)),
              CustomNavigationBarItem(icon: const Icon(LineIcons.receipt)),
              CustomNavigationBarItem(icon: const Icon(Icons.settings_applications_outlined)),
            ],
            currentIndex: controller.pageController.page?.round() ?? 0,
            onTap: (index) => controller.jumpToPage(index),//setState(() => _currentIndex = index),
          ),
        );
      }
    );
  }

}