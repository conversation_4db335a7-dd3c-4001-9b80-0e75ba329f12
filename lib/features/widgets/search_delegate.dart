import 'package:controle_fiado_app/service/database/database_hive_api.dart';
import 'package:flutter/material.dart';

import '../../models/debtor_model.dart';

class DebtorSearchDelegate extends SearchDelegate {
  final List<DebtorObject> _list = HiveDb.fetchDebtors()..sort((a, b) => a.name.compareTo(b.name));

  regex(String search) => RegExp("\\b$search", caseSensitive: false);

  filtering(String query) {
    return _list.where((element) => regex(query).hasMatch(element.name)).toList();
  }

  @override
  List<Widget>? buildActions(BuildContext context) => [
        IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () => query = "",
        )
      ];

  @override
  Widget? buildLeading(BuildContext context) {
    return IconButton(
        onPressed: () { close(context, null); },
        icon: const Icon(Icons.arrow_back)
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    final resultList = filtering(query);

    return ListView.builder(
        itemCount: resultList.length,
        itemBuilder: (ctx, index) => ListTile(
              title: Text(resultList[index]),
              onTap: () {
                close(ctx, resultList[index]);
              },
        )
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    final resultList = filtering(query);

    return ListView.builder(
        itemCount: resultList.length,
        itemBuilder: (ctx, index) => ListTile(
          title: Text(resultList[index].name),
          onTap: () {
            close(ctx, resultList[index]);
          },
        )
    );
  }
}