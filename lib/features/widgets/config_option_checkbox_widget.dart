
import 'package:flutter/material.dart';

class ConfigOptionCheckbox extends StatefulWidget {
  const ConfigOptionCheckbox({Key? key, required this.initialValue, required this.title, required this.onUpdate}) : super(key: key);
  final bool initialValue;
  final String title;
  final void Function(bool changed) onUpdate;

  @override
   State<ConfigOptionCheckbox> createState() => _ConfigOptionCheckboxState();
}

class _ConfigOptionCheckboxState extends State<ConfigOptionCheckbox> {
  late bool enabled;
  final toggle = { true: "ativado", false: "desativado" };

  @override
  void initState() {
    super.initState();
    enabled = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(widget.title),
      subtitle: Text(toggle[enabled]!),
      trailing: Checkbox(
        value: enabled,
        onChanged: (change) {
          setState(() => enabled = change!);
          widget.onUpdate(change!);
        }
      ),
    );
  }
}