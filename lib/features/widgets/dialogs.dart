import 'package:flutter/material.dart';
import 'package:flutter_multi_formatter/flutter_multi_formatter.dart';
import 'package:line_icons/line_icons.dart';
import 'package:toast/toast.dart';

import '../../constants.dart';
import '../../extensions.dart';
import '../../models/debtor_model.dart';
import '../../models/operation_model.dart';
import '../../service/database/database_hive_api.dart';
import '../../utils.dart';
import 'extensions_widgets.dart';
import 'extra_widgets.dart' show Conditional, ConditionalColumn;
import 'flipping_card.dart';

const decorationTF = InputDecoration(
    isDense: true,
    contentPadding: EdgeInsets.symmetric(vertical: 5)
);

/// Monetary: https://stackoverflow.com/a/66062883/3443949
class PersonDialog extends StatefulWidget {
  const PersonDialog(this.debtorObject, {Key? key}) : super(key: key);

  final DebtorObject? debtorObject;

  @override
  State<PersonDialog> createState() => _PersonDialogState();
}

class _PersonDialogState extends State<PersonDialog> {
  final nameController = TextEditingController();
  final phoneController = TextEditingController();
  final valueController = TextEditingController(text: 'R\$ ');//MoneyMaskedTextController(leftSymbol: 'R\$ ');
  var enableValue = true;

  @override
  void initState() {
    super.initState();
    if (widget.debtorObject != null) {
      nameController.text = widget.debtorObject!.name;
      phoneController.text = widget.debtorObject!.phone;
      valueController.updateValue(widget.debtorObject!.value);
      // se já existir um nome é porque está editando pessoa:
      if (widget.debtorObject!.name.isNotEmpty) enableValue = false;
    }

    nameController.addListener(() => setState(() {}));
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      elevation: 8,
      backgroundColor: Colors.transparent,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            decoration: const BoxDecoration(
                color: Colors.lightBlue,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20))
            ),
            child: const SizedBox(
                width: double.infinity, height: 40,
                child: Center(child: Text('Dados de Devedor', style: TextStyle(fontSize: 20))),
              ),
            ),

          Container(
            padding: const EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 5),
            decoration: const BoxDecoration(
                shape: BoxShape.rectangle,
                color: Colors.white,
                borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10)
                ),
                boxShadow: [
                  BoxShadow(color: Colors.black,offset: Offset(0,10), blurRadius: 10),
                ]
            ),

            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Nome*:'),
                TextField(
                  controller: nameController,
                  decoration: decorationTF,
                  keyboardType: TextInputType.name,
                  textInputAction: TextInputAction.next,
                ),
                const SizedBox(height: 15,),

                const Text('Contato'),
                TextFormField(
                  controller: phoneController,
                  inputFormatters: [PhoneMaskFormatter()],
                  decoration: decorationTF,
                  keyboardType: TextInputType.phone,
                  textInputAction: TextInputAction.done,
                ),
                const SizedBox(height: 10,),
                // value block:
                Conditional(
                  condition: enableValue,
                  isTrue: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Valor devido (opcional)'),
                      TextFormField(
                        controller: valueController,
                        decoration: decorationTF,
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          /*PosInputFormatter(
                            decimalSeparator: DecimalPosSeparator.comma,
                            thousandsSeparator: ThousandsPosSeparator.parse('.'),
                          ),*/
                          CurrencyInputFormatter(
                            leadingSymbol: 'R\$',
                            thousandSeparator: ThousandSeparator.Period,
                            useSymbolPadding: true,
                          ),
                        ],
                        textInputAction: TextInputAction.go,
                        onFieldSubmitted: (_) {
                          _onAddPerson();
                          Navigator.pop(context);
                        },
                      ),
                      const SizedBox(height: 10)
                    ],
                  ),
                ),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Conditional(
                      condition: !enableValue,
                      isTrue: TextButton(
                          onPressed: () {
                            Navigator.pop(context, "delete");
                          },
                          child: const Text("Apagar", style: TextStyle(color: Colors.redAccent))
                      )
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text("Cancelar")
                        ), // Cancelar
                        SizedBox(
                          width: 40,
                          child: TextButton(
                              onPressed: nameController.value.text.isEmpty ? null
                                : () {
                                  _onAddPerson();
                                  Navigator.of(context).pop();
                                },
                              child: const Text("Ok")
                          ),
                        ), // OK
                      ],
                    ),
                  ],
                ) // BOTÕES
              ],
            ),
          ),
        ],
      ),
    );
  }

  _onAddPerson() async {
    // var box = HiveDb.boxDebtor;

    var debtor = (widget.debtorObject ?? DebtorObject.empty())
          ..name = _nameText
          ..phone = phoneController.text
          ..value = _valueNumber
          ..identifier = generateFourIdentifier;

    HiveDb.putDebtor(debtor); // adiciona ou atualiza

    if (_valueNumber > 0.0) {
      final operation = OperationObject(
          debtorID: debtor.identifier,
          date: TODAY,
          description: "valor inicial",
          value: _valueNumber);
      HiveDb.putHistoric(operation);
      // HiveDb.putDebtorHistoric(debtor.identifier, operation, box); // adiciona também um histórico próprio do devedor
    } // adicionar valor
  }

  get _nameText => nameController.text;
  get _valueNumber => valueController.numberValue;
}

class OperationDialog extends StatefulWidget {
  const OperationDialog(this.operationObject, {required this.debtor, Key? key}) : super(key: key);

  final DebtorObject debtor;
  final OperationObject? operationObject;

  @override
  State<OperationDialog> createState() => _OperationDialogState();
}

class _OperationDialogState extends State<OperationDialog> {
  final descController = TextEditingController();
  final dateController = TextEditingController();
  late var valueMoneyController = TextEditingController(text: 'R\$ ');

  var _isError = false;
  var _isCompact = Config.compactDialog;
  var _positiveValue = true;
  get _colorError => (_isError) ? Colors.red : Colors.blue;

  @override
  void initState() {
    super.initState();

    /*valueMoneyController = *//*Config.decimalValue
        ? MoneyMaskedTextController(leftSymbol: 'R\$ ')
        :*//* TextEditingController(text: 'R\$ ');*/

    if (widget.operationObject != null) {
      descController.text = widget.operationObject!.description;
      dateController.text = widget.operationObject!.date;
      valueMoneyController.updateValue(widget.operationObject!.value);
      _positiveValue = widget.operationObject!.value >= .0;
    } else {
      dateController.text = TODAY;
    }
  }

  _testError(String value) {
    setState(() {
      if (value.length == 2) _isError = int.parse(value.substring(0, 2)) > 31;
      if (value.length == 4) _isError = int.parse(value[3]) > 1;
      if (value.length == 5) _isError = int.parse(value.substring(3, 5)) > 12;
      if (value.length == 10) _isError = int.parse(value.substring(6, 10)) > 2022;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      elevation: 8,
      backgroundColor: Colors.transparent,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // top of the dialog:
          Container(
            decoration: const BoxDecoration(
                color: Colors.lightBlue,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20))
            ),
            child: const SizedBox(
              width: double.infinity, height: 40,
              child: Center(child: Text('Dados de operação', style: TextStyle(fontSize: 20))),
            ),
          ),

          // dialog's body:
          Container(
            padding: const EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 5),
            decoration: const BoxDecoration(
                shape: BoxShape.rectangle,
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(10),
                    bottomRight: Radius.circular(10)
                ),
                boxShadow: [
                  BoxShadow(color: Colors.black,offset: Offset(0,10), blurRadius: 10),
                ]
            ),
            // body elements:
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Data'),
                TextField(
                  onChanged: _testError,
                  controller: dateController,
                  inputFormatters: [
                    DateMaskFormatter(sample: "xx/xx/xxxx", separator: "/"),
                  ],
                  decoration: decorationTF.copyWith(
                      focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: _colorError)),
                      enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: _colorError))
                  ),
                  keyboardType: TextInputType.datetime,
                  textInputAction: TextInputAction.next,
                ),
                const SizedBox(height: 10),
                // description
                ConditionalColumn(condition: !_isCompact,
                  isTrueChildren: [
                    const Text('Descrição'),
                    TextField(
                      controller: descController,
                      decoration: decorationTF,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.next,
                    ),
                    const SizedBox(height: 10),
                  ],
                ),

                const Text('Valor'),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Select value SIGNAL
                    Expanded(
                      flex: 6,
                      child: Container(
                        constraints: BoxConstraints.tight(const Size.square(30)),
                        child: FlipCard.colors(
                            initialValue: _positiveValue,
                            onChanged: (change) => _positiveValue = change,
                            front: const Icon(Icons.add_circle_outline),
                            rear: const Icon(LineIcons.minusCircle)
                        ),
                      ).paddingOnly(top: 5)
                    ),
                    Expanded(flex: 3, child: Container()),
                    // text controller for value
                    Expanded(
                      flex: 50,
                      child: TextFormField(
                        autofocus: true,
                        controller: valueMoneyController,
                        decoration: decorationTF,
                        keyboardType: Config.decimalValue
                            ? TextInputType.phone
                            : const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          // FilteringTextInputFormatter.digitsOnly,
                          CurrencyInputFormatter(
                            leadingSymbol: 'R\$',
                            thousandSeparator: ThousandSeparator.Period,
                            useSymbolPadding: true,
                          ),
                        ],
                        textInputAction: TextInputAction.go,
                        onFieldSubmitted: (_) {
                          _onAddOperation();
                          Navigator.pop(context);
                        },
                      ),
                    )
                  ],
                ).paddingOnly(left: 3),
                const SizedBox(height: 10),
                // operations line:
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // checkbox compact mode
                    Row(
                      children: [
                        SizedBox(
                            width: 20,
                            child: Checkbox(
                                value: _isCompact,
                                onChanged: (set) => setState(() => _isCompact = set!)
                            )
                        ),
                        const SizedBox(width: 5),
                        const Text("Modo Compacto", style: TextStyle(fontSize: 12)),
                      ],
                    ),
                    // buttons:
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Conditional(
                            condition: !Config.isCancelOut,
                            isTrue: TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text("Cancelar")
                            )
                        ), // Cancelar
                        SizedBox(
                          width: Config.isCancelOut ? 120 : 40,
                          child: TextButton(
                              style: Config.isCancelOut ? TextButton.styleFrom(backgroundColor: Colors.blue, foregroundColor: Colors.white) : null,
                              onPressed: (_isError)
                                ? null
                                : () {
                                    if (widget.operationObject == null) {
                                      _onAddOperation();
                                      Navigator.of(context).pop();
                                    } else {
                                      Navigator.pop(context,
                                          OperationObject(
                                                debtorID: _identifier,
                                                date: _dateText,
                                                description: _descText,
                                                value: _valueNumber)
                                      );
                                    }
                                  },
                              child: const Text("Ok")
                          ),
                        ), // OK
                      ],
                    ),
                  ],
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  /// constrói o layout do campo de descrição, encapsulado pois é condicional
  // ignore: non_constant_identifier_names
  Widget DescriptionWidget() => Conditional(
      condition: !_isCompact,
      isTrue: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Descrição'),
          TextField(
            controller: descController,
            decoration: decorationTF,
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.next,
          ),
          const SizedBox(height: 10),
        ],
      ));

  void _onAddOperation() {
    if (_valueNumber == 0) {
      Toast.show("Valor zero não é adicionado", duration: Toast.lengthLong);
      return;
    }
    var operation = OperationObject(debtorID: _identifier, date: _dateText, description: _descText, value: _valueNumber);

    widget.debtor.value += _valueNumber;
    HiveDb.putDebtor(widget.debtor);

    // HiveDb.putDebtorHistoric(_identifier, operation, null);
    HiveDb.putHistoric(operation);
  }

  get _identifier => widget.debtor.identifier;
  get _dateText => dateController.text;
  get _descText => descController.text;
  get _valueNumber => (_positiveValue) ? (valueMoneyController.numberValue) : (valueMoneyController.numberValue * -1);
}

class BackupDialog extends StatelessWidget {
  const BackupDialog({Key? key, required this.debtor, required this.debtorOperations}) : super(key: key);
  final DebtorObject debtor;
  final List<OperationObject> debtorOperations;

  @override
  Widget build(BuildContext context) {
    final textController = TextEditingController(text: debtor.name.fileNameTimestamp);
    const heightBt = 35.0;
    const widthBt = 65.0;

    return Dialog(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 5),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text("Salvar dados de ${debtor.name}"),
            TextField(
              controller: textController,
              decoration: decorationTF,
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                    width: widthBt,
                    height: heightBt,
                    child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text("Cancelar"))
                ), // BOTÃO CANCELAR
                SizedBox(
                    width: widthBt,
                    height: heightBt,
                    child: TextButton(
                        onPressed: () => Navigator.pop(context, textController.text),
                        child: const Text("Salvar"))
                ), // BOTÃO SALVR
              ],
            ) // BOTÕES
          ],
        ),
      ),
    );
  }
}
