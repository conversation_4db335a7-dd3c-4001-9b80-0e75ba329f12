import 'dart:math';

import 'package:flutter/material.dart';
import 'package:supercharged/supercharged.dart';

class FlipCard extends StatefulWidget {
  FlipCard(
      {Key? key,
        required this.front, required this.rear,
        required this.frontColor,
        required this.rearColor ,
        this.initialFlip = true,
        this.onChanged,
      }) : super(key: key);
  FlipCard.colors({required initialValue, front, required rear, required Function(bool) onChanged})
      : this(
            initialFlip: initialValue,
            front: front,
            rear: rear,
            frontColor: (Colors.green[400] as Color),
            rearColor: (Colors.red[400] as Color),
            onChanged: onChanged
  );

  bool initialFlip;
  final Widget front, rear;
  final Color frontColor, rearColor;
  final Function(bool value)? onChanged;

  @override
  State<FlipCard> createState() => _FlipCardState();
}

class _FlipCardState extends State<FlipCard> {
  late bool _showFront;
  bool _flipXAxix = true;

  @override
  void initState() {
    super.initState();
    _showFront = widget.initialFlip;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() => _showFront = !_showFront);
        widget.onChanged?.call(_showFront);
      },
      child: AnimatedSwitcher(
        duration: 600.milliseconds,
        transitionBuilder: _transitionBuilder,
        child: _showFront ? _buildFront() : _buildRear(),
      ),
    );
  }

  _buildFront() {
    return __buildLayout(
      key: const ValueKey(true),
      backColor: widget.frontColor,
      child: widget.front
    );
  }

  _buildRear() {
    return __buildLayout(
      key: const ValueKey(false),
      backColor: widget.rearColor,
      child: widget.rear
    );
  }

  Widget __buildLayout({required Key key, required Widget child, required Color backColor}) {
    return Container(
      key: key,
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(20.0),
        color: backColor,
      ),
      child: child,
    );
  }

  Widget _transitionBuilder(Widget widget, Animation<double> animation) {
    final rotateAnim = Tween(begin: pi, end: 0.0).animate(animation);
    return AnimatedBuilder(
        animation: rotateAnim,
        child: widget,
        builder: (context, widget) {
          final isUnder = (ValueKey(_showFront) != widget!.key);
          var tilt = ((animation.value - 0.5).abs() - .5) * .003;
          tilt *= isUnder ? -1.0 : 1.0;
          final value = isUnder ? min(rotateAnim.value, pi/2) : rotateAnim.value;

          return Transform(
              transform: (Matrix4.rotationY(value)..setEntry(3, 0, tilt)),
              alignment: Alignment.center,
              child: widget
          );
        }
    );
  }
}