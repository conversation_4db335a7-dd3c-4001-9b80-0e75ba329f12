import 'package:flutter/material.dart';
import 'package:flutter_multi_formatter/formatters/formatter_utils.dart';

extension PaddingMethod on Widget {
  Widget paddingAll(double val) {
    return Padding(
      padding: EdgeInsets.all(val),
      child: this,
    );
  }

  Widget paddingSymmetric({required double horizontal, required double vertical}) => Padding(
    padding: EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical),
    child: this,
  );

  Widget paddingOnly({double left: .0, double top: .0, double right: .0, double bottom: .0}) => Padding(
    padding: EdgeInsets.only(left: left, top: top, right: right, bottom: bottom),
    child: this,
  );
}

extension UtilityContext on BuildContext {
  void nextTextFocus() {
    do {
      FocusScope.of(this).nextFocus();
    } while (FocusScope.of(this).focusedChild!.context == null);
  }

  void unfocus() {
    FocusScope.of(this).focusedChild!.unfocus();
  }
}

extension ValueController on TextEditingController {
  void updateValue(double value) {
    /*if (this is MoneyMaskedTextController) {
      (this as MoneyMaskedTextController).updateValue(value);
    } else {*/
      text = "R\$ ${(value * 100) / 100}";

  }

  double get numberValue {
    // if (this is MoneyMaskedTextController) return (this as MoneyMaskedTextController).numberValue;
    var content = toNumericString(text, errorText: 'não é um número');
    return double.parse(content) / 100;
  }
}
