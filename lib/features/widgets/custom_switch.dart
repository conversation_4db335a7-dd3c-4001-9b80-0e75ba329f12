import 'dart:math';

import 'package:flutter/material.dart';
import 'package:simple_animations/simple_animations.dart';
import 'package:supercharged/supercharged.dart';

import 'extensions_widgets.dart';

enum _SwitchBoxProps { paddingLeft, color, text, rotation }

class CustomSwitch extends StatelessWidget {
  const CustomSwitch ({Key? key, required this.switched}) : super(key: key);
  final bool switched;

  @override
  Widget build(BuildContext context) {
    /*var tween = MultiTween<_SwitchBoxProps>()
      ..add(_SwitchBoxProps.paddingLeft, 0.0.tweenTo(30.0), 1.seconds)
      ..add(_SwitchBoxProps.color, Colors.red.tweenTo(Colors.green), 1.seconds)
      ..add(_SwitchBoxProps.text, ConstantTween("-"), 500.milliseconds)
      ..add(_SwitchBoxProps.text, ConstantTween("+"), 500.milliseconds)
      ..add(_SwitchBoxProps.rotation, (-2 * pi).tweenTo(0), 1.seconds);*/
    var tween = MovieTween()
      ..tween(_SwitchBoxProps.paddingLeft, 0.0.tweenTo(30.0), duration: 1.seconds)
      ..tween(_SwitchBoxProps.color, Colors.red.tweenTo(Colors.green), duration: 1.seconds)
      ..tween(_SwitchBoxProps.text, ConstantTween("-"), duration: 500.milliseconds)
      .thenTween(_SwitchBoxProps.text, ConstantTween("+"), duration: 500.milliseconds)
      ..tween(_SwitchBoxProps.rotation, (-2 * pi).tweenTo(0), duration: 1.seconds);

    return CustomAnimationBuilder(
      control: switched ? Control.play : Control.playReverse,
      startPosition: switched ? 1.0 : .0,
      duration: tween.duration * 1.2,
      tween: tween,
      curve: Curves.easeInOut,
      builder: _builderSwitchBox,
    );
  }

  Widget _builderSwitchBox(context, value, _) {
    return Container(
      decoration: _outerBoxDecoration(value.get(_SwitchBoxProps.color)),
      width: 80,
      height: 30,
      padding: const EdgeInsets.all(3),
      child: Stack(
        children: [
          Positioned(
              child: Transform.rotate(
                angle: value.get(_SwitchBoxProps.rotation),
                child: Container(
                  decoration: _innerBoxDecoration(value.get(_SwitchBoxProps.color)),
                  width: 20,
                  child: Center(child: Text(value.get(_SwitchBoxProps.text), style: labelStyle)),
                ),
              ).paddingOnly(left: value.get(_SwitchBoxProps.paddingLeft))
          )
        ],
      )
    );
  }

  _outerBoxDecoration(Color color) => BoxDecoration(
      borderRadius: const BorderRadius.all(Radius.circular(30)),
      border: Border.all(width: 2, color: color)
  );

  _innerBoxDecoration(Color color) => BoxDecoration(
      borderRadius: const BorderRadius.all(Radius.circular(25)),
      color: color
  );

  static const labelStyle = TextStyle(
    height: 1.3,
    fontWeight: FontWeight.bold,
    fontSize: 12,
    color: Colors.white
  );
}