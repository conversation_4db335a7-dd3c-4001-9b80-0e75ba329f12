// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';
import 'extensions_widgets.dart';

Widget CircularContainer({double size = 50, Color? borderColor,
    required Color innerColor,
    double borderWidth = 1,
    required Widget body}) =>

    Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
          color: borderColor,
          shape: BoxShape.circle
      ),

      child: Container(
          decoration: BoxDecoration(
              color: innerColor,
              shape: BoxShape.circle
          ),

          child: body
      ).paddingAll(borderWidth),
    );

Widget Conditional({required bool condition, required Widget isTrue, Widget? isFalse}) {
  return condition
      ? isTrue
      : (isFalse == null)
          ? const SizedBox.shrink()
          : isFalse;
}

Widget ConditionalColumn({required bool condition, required List<Widget> isTrueChildren, Widget? isFalse, crossAlignment = CrossAxisAlignment.start}) {
  return condition
      ? Column(
          crossAxisAlignment: crossAlignment,
          children: isTrueChildren,
        )
      : (isFalse == null)
          ? const SizedBox.shrink()
          : isFalse;
}

Widget CircularIconButton(IconData iconData, {Function()? onPressed, Color iconColor = Colors.white}) {
  return Container(
    decoration: BoxDecoration(
        color: Colors.white.withOpacity(.25),
        shape: BoxShape.circle
    ),
    child: IconButton(
        onPressed: onPressed,
        icon: Icon(iconData, color: iconColor)
    ),
  );
}