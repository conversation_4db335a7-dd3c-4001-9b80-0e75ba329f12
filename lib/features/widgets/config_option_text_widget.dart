import 'package:flutter/material.dart';
import 'package:toast/toast.dart';

class ConfigOptionTextField extends StatefulWidget {
  const ConfigOptionTextField({super.key, required this.shopName, required this.onUpdate});
  final String shopName;
  final void Function(String) onUpdate;

  @override
  State<ConfigOptionTextField> createState() => _ConfigOptionTextFieldState();
}

class _ConfigOptionTextFieldState extends State<ConfigOptionTextField> {
  late final TextEditingController nameShopController;
  bool readOnly = true;

  @override
  void initState() {
    super.initState();
    ToastContext().init(context);
    nameShopController = TextEditingController(text: widget.shopName);
  }

  void _toggleReadOnly() => setState(() => readOnly = !readOnly);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (readOnly) Toast.show("Clique duas vezes para editar", gravity: Toast.center);
      },
      onDoubleTap: _toggleReadOnly,
      child: ListTile(
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Expanded(
                flex: 2,
                child: Text(
                  "Nome do comércio: ",
                  style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                )),
            const Spacer(flex: 1),

            Expanded(
              flex: 9,
              child: TextField(
                decoration: InputDecoration(
                  floatingLabelBehavior: FloatingLabelBehavior.never,
                  disabledBorder: const UnderlineInputBorder(//fixme corrigir cores para quando não estiver habilitado
                    borderSide: BorderSide(color: Colors.grey),
                  ),
                  hintText: widget.shopName,
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    isDense: true,
                ),
                controller: nameShopController,
                readOnly: readOnly,
              ),
            ),

            !readOnly
                ? Expanded(
                    flex: 1,
                    child: Container(
                      padding: const EdgeInsets.all(0),
                      child: IconButton(
                        onPressed: () {
                          widget.onUpdate(nameShopController.text);
                          _toggleReadOnly();
                        },
                        visualDensity: VisualDensity.compact,
                        icon: const Icon(Icons.one_k_outlined),
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ],
        ),
      ),
    );
  }
}