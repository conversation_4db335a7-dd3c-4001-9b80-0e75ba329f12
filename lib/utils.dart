import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// source: https://github.com/odesenvolvedor/phoneMaskDart/blob/master/phone-mask.dart
class PhoneMaskFormatter extends TextInputFormatter {

  String _phoneMask(String phone) {
    String onlyNumbers = phone.replaceAll(RegExp(r'[\Dg]'), "");
    onlyNumbers = onlyNumbers.replaceAll(RegExp(r'^0'), "");

    if (onlyNumbers.length > 10) {
      onlyNumbers = onlyNumbers.replaceAllMapped(
          RegExp(r'^(.{2})(.{0,5})(.{0,4}).*'),
              (Match m) => "(${m[1]}) ${m[2]}-${m[3]}");
    } else if (onlyNumbers.length > 6) {
      onlyNumbers = onlyNumbers.replaceAllMapped(RegExp(r'^(.{2})(.{0,4})(.{0,4}).*'),
              (Match m) => "(${m[1]}) ${m[2]}-${m[3]}");
    } else if (onlyNumbers.length > 2) {
      onlyNumbers = onlyNumbers.replaceAllMapped(
          RegExp(r'^(.{2})(.{0,4}).*'), (Match m) => "(${m[1]}) ${m[2]}");
    } else if (onlyNumbers.length > 2) {
      onlyNumbers = onlyNumbers.replaceAllMapped(
          RegExp(r'^(.{0,2}).*'), (Match m) => "(${m[0]})");
    } else if (onlyNumbers.isNotEmpty) {
      onlyNumbers = onlyNumbers.replaceAllMapped(
          RegExp(r'^(.{0,2}).*'), (Match m) => "(${m[0]}");
    } else {
      onlyNumbers = '';
    }
    return onlyNumbers;
  }

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {

    String newText = _phoneMask(newValue.text);
    int offset = newValue.selection.base.offset < newValue.text.length
        ? newValue.selection.base.offset
        : newText.length;

    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(
        offset: offset,
      ),
    );
  }
}

class DateMaskFormatter extends TextInputFormatter {
  DateMaskFormatter({required this.sample, required this.separator});

  final String sample;
  final String separator;

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    var dateTxt = newValue.text;
    if (dateTxt.isNotEmpty) { // ignora se for mais que 10 dígitos
      if (dateTxt.length > oldValue.text.length) {
        if (dateTxt.length > sample.length) return oldValue; // caso ultrapasse o limite

        if (dateTxt.length < sample.length && sample[dateTxt.length - 1] == separator) {
          return TextEditingValue(
            text: '${oldValue.text}$separator${dateTxt.substring(dateTxt.length - 1)}',
            selection: TextSelection.collapsed(offset: newValue.selection.end + 1)
          );
        }
      }
    }
    return newValue;
  }
}